"""
⚙️ CONFIGURATION CENTER - config.py

This file stores ALL the settings for your whale tracker:

1. 🔑 API Keys (Telegram, Etherscan, etc.)
2. 💰 Whale threshold ($500k default)
3. ⏰ When to send daily reports (9 AM UTC)
4. 🌐 API URLs and rate limits
5. 📊 Google Sheets settings

WHAT IT DOES:
- Loads your API keys from the .env file
- Sets up rate limits to respect free API tiers
- Defines what counts as a "whale" transaction
- Configures logging and error handling

HOW TO USE:
- You don't run this directly
- Edit your .env file to change settings
- The other modules import settings from here

This is like the "settings menu" for your whale tracker!
"""

import os
from dotenv import load_dotenv

# Load environment variables from .env file
load_dotenv()

# API Keys (set these in your .env file)
TELEGRAM_BOT_TOKEN = os.getenv('TELEGRAM_BOT_TOKEN')
TELEGRAM_CHAT_ID = os.getenv('TELEGRAM_CHAT_ID')  # Your private chat ID
ETHERSCAN_API_KEY = os.getenv('ETHERSCAN_API_KEY')  # Free tier: 5 calls/sec, 100k calls/day
COINGECKO_API_KEY = os.getenv('COINGECKO_API_KEY', None)  # Optional, free tier without key
BINANCE_API_KEY = os.getenv('BINANCE_API_KEY')  # For Binance exchange balances
BINANCE_API_SECRET = os.getenv('BINANCE_API_SECRET')  # For Binance exchange balances

# Google Sheets Configuration
def get_google_credentials_file():
    """Get Google credentials file path for cloud deployment"""
    import base64
    import tempfile
    import logging

    # Try base64 encoded credentials first (Render/Railway)
    base64_creds = os.getenv('GOOGLE_CREDENTIALS_BASE64')
    if base64_creds:
        try:
            # Decode and create temporary file
            creds_json = base64.b64decode(base64_creds).decode('utf-8')
            temp_file = tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False)
            temp_file.write(creds_json)
            temp_file.close()
            return temp_file.name
        except Exception as e:
            logging.error(f"Error decoding base64 credentials: {e}")

    # Try JSON string credentials (alternative)
    json_creds = os.getenv('GOOGLE_CREDENTIALS_JSON')
    if json_creds:
        try:
            temp_file = tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False)
            temp_file.write(json_creds)
            temp_file.close()
            return temp_file.name
        except Exception as e:
            logging.error(f"Error creating credentials from JSON: {e}")

    # Fallback to local file
    return os.getenv('GOOGLE_SHEETS_CREDENTIALS_FILE', 'credentials.json')

GOOGLE_SHEETS_CREDENTIALS_FILE = get_google_credentials_file()
GOOGLE_SHEETS_ID = os.getenv('GOOGLE_SHEETS_ID')  # Your Google Sheet ID

# API Endpoints
ETHERSCAN_BASE_URL = "https://api.etherscan.io/api"
BLOCKCHAIN_INFO_BASE_URL = "https://blockchain.info"
COINGECKO_BASE_URL = "https://api.coingecko.com/api/v3"

# Whale Transaction Thresholds (in USD)
WHALE_THRESHOLD_USD = 500000  # $500k minimum

# ETH specific thresholds (will be calculated based on current price)
MIN_ETH_AMOUNT = 100  # Minimum ETH amount to consider (will adjust based on price)

# BTC specific thresholds  
MIN_BTC_AMOUNT = 10  # Minimum BTC amount to consider (will adjust based on price)

# Rate Limiting Configuration (respecting free tier limits)
ETHERSCAN_RATE_LIMIT = 0.2  # 5 calls per second = 0.2 seconds between calls
COINGECKO_RATE_LIMIT = 1.0  # Conservative rate limiting for free tier
BLOCKCHAIN_INFO_RATE_LIMIT = 0.1  # No official limit, but be conservative

# Time Configuration
DAILY_CHECK_TIME = "09:00"  # Time to run daily check (24-hour format)
LOOKBACK_HOURS = 24  # How many hours back to look for transactions

# Telegram Message Configuration
MAX_TRANSACTIONS_PER_MESSAGE = 10  # Limit to avoid message length issues
MESSAGE_CHUNK_SIZE = 4000  # Telegram message limit is 4096 characters

# Google Sheets Configuration
SHEET_NAME = "Whale Transactions"
SHEET_HEADERS = [
    "Timestamp",
    "Blockchain",
    "Transaction Hash",
    "From Address",
    "To Address",
    "Amount",
    "Currency",
    "USD Value",
    "Current Price",
    "Block Number",
    # Enhanced learning data columns
    "Price_Before_1h",
    "Price_After_1h",
    "Price_After_6h",
    "From_Exchange",
    "To_Exchange",
    "Transaction_Type",
    "Alert_Sent",
    "Round_Amount",
    "User_Rating"
]

# Learning and Filtering Configuration
LEARNING_ENABLED = True
BASIC_FILTERING_ENABLED = True

# Round amount thresholds (likely exchange internal transfers)
ROUND_AMOUNT_THRESHOLDS = {
    'BTC': [1, 5, 10, 25, 50, 100, 500, 1000],
    'ETH': [10, 50, 100, 500, 1000, 5000, 10000],
    'WLD': [10000, 50000, 100000, 500000, 1000000],
    'ADA': [100000, 500000, 1000000, 5000000],
    'ONE': [1000000, 5000000, 10000000, 50000000]
}

# Exchange address file path
def get_exchange_addresses_data():
    """Get exchange addresses data for cloud deployment"""
    import json
    import logging

    # Try JSON string from environment variable first (Railway/Render)
    json_data = os.getenv('EXCHANGE_ADDRESSES_JSON')
    if json_data:
        try:
            return json.loads(json_data)
        except Exception as e:
            logging.error(f"Error parsing EXCHANGE_ADDRESSES_JSON: {e}")

    # Fallback to local file
    try:
        with open('exchange_addresses.json', 'r') as f:
            return json.load(f)
    except Exception as e:
        logging.warning(f"Could not load exchange_addresses.json: {e}")
        return {"exchanges": {}}

EXCHANGE_ADDRESSES_FILE = "exchange_addresses.json"

# Price tracking configuration
PRICE_TRACKING_ENABLED = True
PRICE_TRACKING_INTERVAL_MINUTES = 60  # Track prices every hour

# Validation
def validate_config():
    """Validate that required configuration is present"""
    required_vars = [
        ('TELEGRAM_BOT_TOKEN', TELEGRAM_BOT_TOKEN),
        ('TELEGRAM_CHAT_ID', TELEGRAM_CHAT_ID),
        ('ETHERSCAN_API_KEY', ETHERSCAN_API_KEY),
        ('GOOGLE_SHEETS_ID', GOOGLE_SHEETS_ID)
    ]
    
    missing_vars = []
    for var_name, var_value in required_vars:
        if not var_value:
            missing_vars.append(var_name)
    
    if missing_vars:
        raise ValueError(f"Missing required environment variables: {', '.join(missing_vars)}")
    
    return True

# Logging Configuration
LOGGING_CONFIG = {
    'version': 1,
    'disable_existing_loggers': False,
    'formatters': {
        'standard': {
            'format': '%(asctime)s [%(levelname)s] %(name)s: %(message)s'
        },
    },
    'handlers': {
        'default': {
            'level': 'INFO',
            'formatter': 'standard',
            'class': 'logging.StreamHandler',
            'stream': 'ext://sys.stdout',
        },
        'file': {
            'level': 'DEBUG',
            'formatter': 'standard',
            'class': 'logging.FileHandler',
            'filename': 'whale_tracker.log',
            'mode': 'a',
            'encoding': 'utf-8',
        },
    },
    'loggers': {
        '': {
            'handlers': ['default', 'file'],
            'level': 'DEBUG',
            'propagate': False
        }
    }
}
