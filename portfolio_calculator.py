"""
📊 PORTFOLIO CALCULATOR - portfolio_calculator.py

This is your PERFORMANCE ANALYZER! This file:

1. 🧮 Calculates PnL vs weighted average cost basis
2. 📈 Computes daily performance changes
3. 💰 Tracks unrealized gains/losses
4. 🎯 Provides performance metrics and alerts
5. 📊 Generates portfolio summary data

WHAT IT DOES:
- Combines current balances with historical cost basis
- Calculates performance vs your average buy prices
- Shows both daily changes and lifetime performance
- Identifies significant moves and opportunities

HOW TO USE:
- Called by main portfolio tracker
- Uses data from portfolio_history and api_handler
- Provides formatted performance data for Telegram

This calculates your true crypto performance! 📈
"""

import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple
from portfolio_history import portfolio_history, get_cost_basis
from api_handler import APIHandler

logger = logging.getLogger(__name__)

class PortfolioCalculator:
    """Calculates portfolio performance metrics and PnL"""
    
    def __init__(self):
        self.api_handler = APIHandler()
        self.last_prices = {}
        self.last_update = None
    
    def calculate_portfolio_performance(self) -> Dict:
        """Calculate complete portfolio performance"""
        try:
            # Get current portfolio data
            current_data = self.api_handler.get_portfolio_performance()
            if not current_data:
                logger.error("Failed to get current portfolio data")
                return {}
            
            # Get cost basis data
            cost_basis_data = get_cost_basis()
            
            # Get daily price changes
            daily_changes = self._calculate_daily_changes(current_data['prices'])
            
            # Calculate performance for each coin
            coin_performance = {}
            total_current_value = 0
            total_invested = 0
            
            for coin, balance in current_data['balances'].items():
                if balance <= 0:
                    continue
                
                current_price = current_data['prices'].get(coin, 0)
                current_value = balance * current_price
                
                # Get cost basis info
                cost_info = cost_basis_data.get(coin, {})
                avg_cost = cost_info.get('average_cost', 0)
                total_cost = cost_info.get('total_invested', 0)
                
                # Calculate performance metrics
                performance = self._calculate_coin_performance(
                    coin, balance, current_price, avg_cost, total_cost, daily_changes.get(coin, 0)
                )
                
                coin_performance[coin] = performance
                total_current_value += current_value
                total_invested += total_cost
            
            # Calculate overall portfolio metrics
            overall_performance = self._calculate_overall_performance(
                total_current_value, total_invested, daily_changes
            )
            
            # Combine all data
            portfolio_performance = {
                'timestamp': current_data['timestamp'],
                'total_value': total_current_value,
                'total_invested': total_invested,
                'overall_performance': overall_performance,
                'coins': coin_performance,
                'daily_changes': daily_changes,
                'alerts': self._generate_alerts(coin_performance, overall_performance)
            }
            
            logger.info(f"Portfolio performance calculated: ${total_current_value:.2f} total value")
            return portfolio_performance
            
        except Exception as e:
            logger.error(f"Error calculating portfolio performance: {e}")
            return {}
    
    def _calculate_coin_performance(self, coin: str, balance: float, current_price: float,
                                  avg_cost: float, total_invested: float, daily_change: float) -> Dict:
        """Calculate performance metrics for a single coin"""
        current_value = balance * current_price

        # Calculate price change vs average cost (more reliable than PnL when transaction history incomplete)
        if avg_cost > 0:
            price_vs_avg = ((current_price - avg_cost) / avg_cost) * 100

            # Only calculate PnL if we have reasonable transaction history
            # (if total_invested is much smaller than current_value, history is likely incomplete)
            expected_invested = balance * avg_cost
            history_completeness = total_invested / expected_invested if expected_invested > 0 else 0

            if history_completeness > 0.8:  # Transaction history seems complete
                unrealized_pnl = current_value - total_invested
                pnl_percentage = (unrealized_pnl / total_invested) * 100 if total_invested > 0 else 0
            else:  # Transaction history incomplete, use price change instead
                unrealized_pnl = 0
                pnl_percentage = price_vs_avg  # Show price change as "PnL"
        else:
            unrealized_pnl = 0
            pnl_percentage = 0
            price_vs_avg = 0

        return {
            'coin': coin,
            'balance': round(balance, 6),
            'current_price': round(current_price, 4),
            'current_value': round(current_value, 2),
            'avg_cost': round(avg_cost, 4),
            'total_invested': round(total_invested, 2),
            'unrealized_pnl': round(unrealized_pnl, 2),
            'pnl_percentage': round(pnl_percentage, 2),
            'price_vs_avg_cost': round(price_vs_avg, 2),
            'daily_change': round(daily_change, 2),
            'daily_value_change': round(current_value * (daily_change / 100), 2)
        }
    
    def _calculate_overall_performance(self, total_value: float, total_invested: float,
                                     daily_changes: Dict[str, float]) -> Dict:
        """Calculate overall portfolio performance metrics"""
        # Overall PnL
        overall_pnl = total_value - total_invested
        overall_pnl_percentage = (overall_pnl / total_invested) * 100 if total_invested > 0 else 0

        # Simple average daily change (no need for weighted calculation that causes API loops)
        if daily_changes:
            avg_daily_change = sum(daily_changes.values()) / len(daily_changes)
        else:
            avg_daily_change = 0

        return {
            'total_pnl': round(overall_pnl, 2),
            'total_pnl_percentage': round(overall_pnl_percentage, 2),
            'daily_change': round(avg_daily_change, 2),
            'daily_value_change': round(total_value * (avg_daily_change / 100), 2)
        }
    
    def _calculate_daily_changes(self, current_prices: Dict[str, float]) -> Dict[str, float]:
        """Calculate 24h price changes for each coin"""
        daily_changes = {}
        
        # For now, we'll use CoinGecko's 24h change data
        # This could be enhanced to track our own price history
        try:
            # Get 24h price change data from CoinGecko
            from portfolio_config import get_coingecko_ids
            coingecko_ids = get_coingecko_ids()
            
            for coin, current_price in current_prices.items():
                if coin in coingecko_ids:
                    # This is a simplified approach - in a real implementation,
                    # you'd fetch 24h change data from CoinGecko API
                    daily_changes[coin] = 0.0  # Placeholder
                else:
                    daily_changes[coin] = 0.0
                    
        except Exception as e:
            logger.warning(f"Could not calculate daily changes: {e}")
            # Return zero changes as fallback
            for coin in current_prices.keys():
                daily_changes[coin] = 0.0
        
        return daily_changes
    
    def _generate_alerts(self, coin_performance: Dict, overall_performance: Dict) -> List[str]:
        """Generate performance alerts and suggestions"""
        alerts = []
        
        # Check for significant gains
        for coin, perf in coin_performance.items():
            pnl_pct = perf['pnl_percentage']
            daily_change = perf['daily_change']
            
            # Alert for large gains (consider taking profits)
            if pnl_pct > 75:
                alerts.append(f"🚀 {coin} up {pnl_pct:.1f}% from avg cost - consider trimming?")
            elif pnl_pct > 50:
                alerts.append(f"📈 {coin} up {pnl_pct:.1f}% from avg cost - strong performance!")
            
            # Alert for large daily moves
            if abs(daily_change) > 10:
                direction = "📈" if daily_change > 0 else "📉"
                alerts.append(f"{direction} {coin} moved {daily_change:.1f}% today")
        
        # Overall portfolio alerts
        overall_pnl = overall_performance['total_pnl_percentage']
        if overall_pnl > 100:
            alerts.append(f"🎉 Portfolio doubled! Up {overall_pnl:.1f}% overall")
        elif overall_pnl > 50:
            alerts.append(f"✅ Portfolio up {overall_pnl:.1f}% - excellent performance!")
        elif overall_pnl < -20:
            alerts.append(f"⚠️ Portfolio down {abs(overall_pnl):.1f}% - consider DCA?")
        
        return alerts
    
    def format_portfolio_digest(self, performance_data: Dict) -> str:
        """Format portfolio performance into a readable digest"""
        if not performance_data:
            return "❌ Unable to generate portfolio digest"
        
        # Header
        total_value = performance_data['total_value']
        overall = performance_data['overall_performance']
        daily_change = overall['daily_change']
        total_pnl_pct = overall['total_pnl_percentage']
        
        daily_symbol = "📈" if daily_change >= 0 else "📉"
        pnl_symbol = "📈" if total_pnl_pct >= 0 else "📉"
        
        digest = f"""📊 Portfolio Digest – {datetime.now().strftime('%b %d')}

💰 Total Value: ${total_value:,.2f}
{daily_symbol} Daily: {daily_change:+.1f}% (${overall['daily_value_change']:+,.2f})
{pnl_symbol} Lifetime: {total_pnl_pct:+.1f}% (${overall['total_pnl']:+,.2f})

🎯 Holdings:"""
        
        # Top holdings
        coins = sorted(performance_data['coins'].items(), 
                      key=lambda x: x[1]['current_value'], reverse=True)
        
        for coin, perf in coins[:5]:  # Show top 5 holdings
            value = perf['current_value']
            balance = perf['balance']
            pnl_pct = perf['pnl_percentage']
            avg_cost = perf['avg_cost']
            current_price = perf['current_price']
            
            pnl_emoji = "📈" if pnl_pct >= 0 else "📉"
            
            digest += f"""
• {coin}: {balance:.4f} = ${value:,.2f}
  Avg: ${avg_cost:.4f} | Now: ${current_price:.4f}
  {pnl_emoji} PnL: {pnl_pct:+.1f}%"""
        
        # Alerts
        alerts = performance_data.get('alerts', [])
        if alerts:
            digest += "\n\n🔔 Alerts:"
            for alert in alerts[:3]:  # Show top 3 alerts
                digest += f"\n{alert}"
        
        return digest

# Global instance
portfolio_calculator = PortfolioCalculator()

# Convenience functions
def get_portfolio_performance() -> Dict:
    """Get complete portfolio performance data"""
    return portfolio_calculator.calculate_portfolio_performance()

def get_portfolio_digest() -> str:
    """Get formatted portfolio digest"""
    performance = get_portfolio_performance()
    return portfolio_calculator.format_portfolio_digest(performance)

if __name__ == "__main__":
    # Test the calculator
    try:
        print("📊 Testing Portfolio Calculator...")
        
        performance = get_portfolio_performance()
        if performance:
            print("✅ Portfolio performance calculated successfully")
            print(f"Total Value: ${performance['total_value']:.2f}")
            
            digest = get_portfolio_digest()
            print("\n📱 Portfolio Digest:")
            print(digest)
        else:
            print("❌ Failed to calculate portfolio performance")
            
    except Exception as e:
        print(f"❌ Error testing portfolio calculator: {e}")
