"""
📊 PORTFOLIO CONFIGURATION - portfolio_config.py

This is your PORTFOLIO SETTINGS CENTER! This file:

1. 🏠 Stores your wallet addresses (Ledger, Binance, etc.)
2. 💰 Defines which coins you hold and want to track
3. 📊 Configures portfolio tracking settings
4. 🔑 Manages API keys for balance fetching
5. 📈 Sets up performance calculation parameters

WHAT IT DOES:
- Lists all your wallet addresses across different chains
- Defines which cryptocurrencies to track in your portfolio
- Configures how to fetch balances from different sources
- Sets up cost basis calculation parameters

HOW TO USE:
- Add your wallet addresses to PORTFOLIO_ADDRESSES
- Enable/disable coins you want to track
- Configure exchange API access if needed
- The system will automatically track these addresses

This is your portfolio configuration center! 💼
"""

import os
from typing import Dict, List, Optional
from dataclasses import dataclass
from dotenv import load_dotenv

load_dotenv()

@dataclass
class PortfolioAddress:
    """Configuration for a portfolio address"""
    address: str
    chain: str  # 'BTC', 'ETH', 'ADA', etc.
    label: str  # 'Ledger Wallet', 'Binance', 'MetaMask', etc.
    source: str  # 'blockchain', 'exchange_api', 'manual'
    enabled: bool = True

# Your Portfolio Addresses
# Add your actual wallet addresses here
PORTFOLIO_ADDRESSES = [
    # Your actual wallet addresses

    # Ethereum addresses
    PortfolioAddress(
        address="******************************************",
        chain="ETH",
        label="Ledger Ethereum",
        source="blockchain"
    ),

    # Cardano addresses
    PortfolioAddress(
        address="stake1uxej86awyh34vqth52dh38mms0cvgu3zu7w0t04a8qacuasne4l64",
        chain="ADA",
        label="Ledger Cardano",
        source="blockchain"
    ),

    # USDC addresses (ERC-20 on Ethereum)
    PortfolioAddress(
        address="******************************************",
        chain="USDC",
        label="Ledger USDC",
        source="blockchain"
    ),
]

# Coins to track in your portfolio
PORTFOLIO_COINS = {
    'BTC': {
        'enabled': True,
        'coingecko_id': 'bitcoin',
        'decimals': 8,
        'chain_type': 'bitcoin'
    },
    'ETH': {
        'enabled': True,
        'coingecko_id': 'ethereum', 
        'decimals': 18,
        'chain_type': 'ethereum'
    },
    'ADA': {
        'enabled': True,
        'coingecko_id': 'cardano',
        'decimals': 6,
        'chain_type': 'cardano'
    },
    'WLD': {
        'enabled': False,  # Disabled since you don't hold it
        'coingecko_id': 'worldcoin-wld',
        'decimals': 18,
        'chain_type': 'erc20',
        'contract_address': '******************************************'
    },
    'USDC': {
        'enabled': True,
        'coingecko_id': 'usd-coin',
        'decimals': 6,
        'chain_type': 'erc20',
        'contract_address': '0xA0b86a33E6441b8C4505B8C4505B8C4505B8C4505'  # USDC contract
    },
    # Add more coins as needed
}

# Exchange API Configuration (for fetching exchange balances)
EXCHANGE_CONFIG = {
    'binance': {
        'enabled': True,  # Enabled for your Binance balances
        'api_key_env': 'BINANCE_API_KEY',
        'api_secret_env': 'BINANCE_API_SECRET',
        'testnet': False
    },
    'coinbase': {
        'enabled': False,
        'api_key_env': 'COINBASE_API_KEY',
        'api_secret_env': 'COINBASE_API_SECRET'
    }
}

# Portfolio Calculation Settings
PORTFOLIO_SETTINGS = {
    'cost_basis_method': 'weighted_average',  # 'fifo', 'lifo', 'weighted_average'
    'include_staking_rewards': True,
    'include_airdrops': True,
    'minimum_balance_threshold': 0.001,  # Ignore dust amounts
    'update_frequency_minutes': 60,  # How often to check for updates
    'price_change_alert_threshold': 5.0,  # Alert if any coin moves >5% in 24h
}

# Google Sheets Configuration for Portfolio History
PORTFOLIO_SHEETS_CONFIG = {
    'history_sheet_name': 'Portfolio History',  # Your existing transaction history in Chain Sailor
    'tracking_sheet_name': 'Portfolio_Tracking',  # New sheet for daily snapshots
    'cost_basis_columns': {
        'date': 'Date',
        'coin': 'Coin', 
        'amount': 'Amount',
        'price': 'Price',
        'total_cost': 'Total_Cost',
        'type': 'Type'  # 'buy', 'sell', 'transfer_in', 'transfer_out'
    }
}

def get_enabled_portfolio_coins() -> List[str]:
    """Get list of enabled coins for portfolio tracking"""
    return [coin for coin, config in PORTFOLIO_COINS.items() if config.get('enabled', False)]

def get_portfolio_addresses_by_chain(chain: str) -> List[PortfolioAddress]:
    """Get all addresses for a specific chain"""
    return [addr for addr in PORTFOLIO_ADDRESSES if addr.chain.upper() == chain.upper() and addr.enabled]

def get_all_portfolio_addresses() -> List[PortfolioAddress]:
    """Get all enabled portfolio addresses"""
    return [addr for addr in PORTFOLIO_ADDRESSES if addr.enabled]

def get_coingecko_ids() -> Dict[str, str]:
    """Get mapping of coin symbols to CoinGecko IDs"""
    return {coin: config['coingecko_id'] for coin, config in PORTFOLIO_COINS.items() if config.get('enabled', False)}

def is_exchange_enabled(exchange: str) -> bool:
    """Check if exchange API is enabled"""
    return EXCHANGE_CONFIG.get(exchange, {}).get('enabled', False)

def get_exchange_credentials(exchange: str) -> Dict[str, Optional[str]]:
    """Get exchange API credentials from environment"""
    if not is_exchange_enabled(exchange):
        return {}
    
    config = EXCHANGE_CONFIG.get(exchange, {})
    credentials = {}
    
    if 'api_key_env' in config:
        credentials['api_key'] = os.getenv(config['api_key_env'])
    if 'api_secret_env' in config:
        credentials['api_secret'] = os.getenv(config['api_secret_env'])
        
    return credentials

# Validation function
def validate_portfolio_config():
    """Validate portfolio configuration"""
    errors = []
    
    if not PORTFOLIO_ADDRESSES:
        errors.append("No portfolio addresses configured")
    
    enabled_coins = get_enabled_portfolio_coins()
    if not enabled_coins:
        errors.append("No coins enabled for tracking")
    
    # Check for required environment variables for enabled exchanges
    for exchange, config in EXCHANGE_CONFIG.items():
        if config.get('enabled', False):
            if 'api_key_env' in config and not os.getenv(config['api_key_env']):
                errors.append(f"Missing {config['api_key_env']} for {exchange}")
            if 'api_secret_env' in config and not os.getenv(config['api_secret_env']):
                errors.append(f"Missing {config['api_secret_env']} for {exchange}")
    
    if errors:
        raise ValueError(f"Portfolio configuration errors: {', '.join(errors)}")
    
    return True

if __name__ == "__main__":
    # Test configuration
    try:
        validate_portfolio_config()
        print("✅ Portfolio configuration is valid")
        print(f"📊 Tracking {len(get_enabled_portfolio_coins())} coins")
        print(f"🏠 Monitoring {len(get_all_portfolio_addresses())} addresses")
    except ValueError as e:
        print(f"❌ Configuration error: {e}")
